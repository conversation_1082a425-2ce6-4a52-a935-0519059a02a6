* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

main {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

section {
    margin-bottom: 30px;
}

/* Upload Section */
.upload-area {
    border: 3px dashed #667eea;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
}

.upload-area.dragover {
    border-color: #764ba2;
    background: #e8ebff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-content h3 {
    margin-bottom: 10px;
    color: #333;
}

.browse-text {
    color: #667eea;
    text-decoration: underline;
    cursor: pointer;
}

/* Preview Section */
.image-container {
    text-align: center;
    margin-bottom: 20px;
}

.image-container img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.process-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
    margin: 0 auto;
}

.process-btn:hover {
    transform: translateY(-2px);
}

/* Progress Section */
.progress-bar {
    width: 100%;
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    color: #666;
    font-weight: 500;
}

/* Results Section */
.text-output {
    margin-bottom: 20px;
}

.text-output textarea {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    background: #f9f9f9;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #6c757d;
}

.action-btn.secondary:hover {
    background: #5a6268;
}

/* Error Section */
.error-message {
    text-align: center;
    padding: 20px;
    background: #ffe6e6;
    border: 2px solid #ff6b6b;
    border-radius: 8px;
}

.error-message h3 {
    color: #d63031;
    margin-bottom: 10px;
}

.error-message p {
    color: #636e72;
    margin-bottom: 15px;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 20px;
    color: white;
    opacity: 0.8;
}

footer a {
    color: white;
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    main {
        padding: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.preview-section, .progress-section, .results-section, .error-section {
    animation: fadeIn 0.5s ease;
}
