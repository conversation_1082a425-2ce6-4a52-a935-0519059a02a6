<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image to Text Converter</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/tesseract.js@v5.0.0/dist/tesseract.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>📸 Image to Text Converter</h1>
            <p>Upload an image and extract text using OCR technology</p>
        </header>

        <main>
            <!-- Upload Section -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <h3>Drop your image here</h3>
                        <p>or <span class="browse-text">browse files</span></p>
                        <input type="file" id="imageInput" accept="image/*" hidden>
                    </div>
                </div>
            </section>

            <!-- Preview Section -->
            <section class="preview-section" id="previewSection" style="display: none;">
                <h3>Image Preview</h3>
                <div class="image-container">
                    <img id="imagePreview" alt="Uploaded image preview">
                </div>
                <button id="processBtn" class="process-btn">Extract Text</button>
            </section>

            <!-- Progress Section -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <h3>Processing...</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p class="progress-text" id="progressText">Initializing...</p>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <h3>Extracted Text</h3>
                <div class="text-output">
                    <textarea id="extractedText" readonly placeholder="Extracted text will appear here..."></textarea>
                </div>
                <div class="action-buttons">
                    <button id="copyBtn" class="action-btn">📋 Copy Text</button>
                    <button id="downloadBtn" class="action-btn">💾 Download as TXT</button>
                    <button id="resetBtn" class="action-btn secondary">🔄 Process Another Image</button>
                </div>
            </section>

            <!-- Error Section -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-message">
                    <h3>❌ Error</h3>
                    <p id="errorText"></p>
                    <button id="retryBtn" class="action-btn">Try Again</button>
                </div>
            </section>
        </main>

        <footer>
            <p>Powered by <a href="https://tesseract.projectnaptha.com/" target="_blank">Tesseract.js</a></p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
