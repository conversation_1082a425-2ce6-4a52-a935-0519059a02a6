// DOM Elements
const uploadArea = document.getElementById('uploadArea');
const imageInput = document.getElementById('imageInput');
const previewSection = document.getElementById('previewSection');
const imagePreview = document.getElementById('imagePreview');
const processBtn = document.getElementById('processBtn');
const progressSection = document.getElementById('progressSection');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const resultsSection = document.getElementById('resultsSection');
const extractedText = document.getElementById('extractedText');
const copyBtn = document.getElementById('copyBtn');
const downloadBtn = document.getElementById('downloadBtn');
const resetBtn = document.getElementById('resetBtn');
const errorSection = document.getElementById('errorSection');
const errorText = document.getElementById('errorText');
const retryBtn = document.getElementById('retryBtn');

let currentImageFile = null;

// Initialize event listeners
function initializeEventListeners() {
    // Upload area click
    uploadArea.addEventListener('click', () => imageInput.click());
    
    // File input change
    imageInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // Process button
    processBtn.addEventListener('click', processImage);
    
    // Action buttons
    copyBtn.addEventListener('click', copyText);
    downloadBtn.addEventListener('click', downloadText);
    resetBtn.addEventListener('click', resetApp);
    retryBtn.addEventListener('click', resetApp);
}

// Handle file selection
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
        currentImageFile = file;
        displayImagePreview(file);
    } else {
        showError('Please select a valid image file.');
    }
}

// Handle drag over
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

// Handle drag leave
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

// Handle drop
function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            currentImageFile = file;
            displayImagePreview(file);
        } else {
            showError('Please drop a valid image file.');
        }
    }
}

// Display image preview
function displayImagePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        imagePreview.src = e.target.result;
        hideAllSections();
        previewSection.style.display = 'block';
    };
    reader.readAsDataURL(file);
}

// Process image with Tesseract
async function processImage() {
    if (!currentImageFile) {
        showError('No image selected.');
        return;
    }

    hideAllSections();
    progressSection.style.display = 'block';
    
    try {
        const result = await Tesseract.recognize(
            currentImageFile,
            'eng',
            {
                logger: m => {
                    updateProgress(m);
                }
            }
        );
        
        const text = result.data.text.trim();
        if (text) {
            displayResults(text);
        } else {
            showError('No text could be extracted from the image. Please try with a clearer image containing text.');
        }
    } catch (error) {
        console.error('OCR Error:', error);
        showError('An error occurred while processing the image. Please try again.');
    }
}

// Update progress
function updateProgress(m) {
    const { status, progress } = m;
    
    if (progress) {
        const percentage = Math.round(progress * 100);
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${status}: ${percentage}%`;
    } else {
        progressText.textContent = status;
    }
}

// Display results
function displayResults(text) {
    extractedText.value = text;
    hideAllSections();
    resultsSection.style.display = 'block';
}

// Copy text to clipboard
async function copyText() {
    try {
        await navigator.clipboard.writeText(extractedText.value);
        
        // Visual feedback
        const originalText = copyBtn.textContent;
        copyBtn.textContent = '✅ Copied!';
        copyBtn.style.background = '#28a745';
        
        setTimeout(() => {
            copyBtn.textContent = originalText;
            copyBtn.style.background = '#667eea';
        }, 2000);
    } catch (error) {
        // Fallback for older browsers
        extractedText.select();
        document.execCommand('copy');
        
        const originalText = copyBtn.textContent;
        copyBtn.textContent = '✅ Copied!';
        setTimeout(() => {
            copyBtn.textContent = originalText;
        }, 2000);
    }
}

// Download text as file
function downloadText() {
    const text = extractedText.value;
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted-text.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Reset application
function resetApp() {
    currentImageFile = null;
    imageInput.value = '';
    extractedText.value = '';
    hideAllSections();
    
    // Reset progress
    progressFill.style.width = '0%';
    progressText.textContent = 'Initializing...';
}

// Show error
function showError(message) {
    errorText.textContent = message;
    hideAllSections();
    errorSection.style.display = 'block';
}

// Hide all sections
function hideAllSections() {
    previewSection.style.display = 'none';
    progressSection.style.display = 'none';
    resultsSection.style.display = 'none';
    errorSection.style.display = 'none';
}

// Initialize the application
document.addEventListener('DOMContentLoaded', initializeEventListeners);
